import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import { UserList } from '../UserList';
import { useUserStore } from '../../stores/userStore';
import { useThemeStore } from '../../stores/themeStore';

// Mock the stores
vi.mock('../../stores/userStore');
vi.mock('../../stores/themeStore');

const mockUseUserStore = vi.mocked(useUserStore);
const mockUseThemeStore = vi.mocked(useThemeStore);

describe('UserList', () => {
  beforeEach(() => {
    mockUseThemeStore.mockReturnValue({
      colors: {
        primary: '#3b82f6',
        secondary: '#6366f1',
      },
    });
  });

  it('renders loading state', () => {
    mockUseUserStore.mockReturnValue({
      users: [],
      loading: true,
      error: null,
      fetchUsers: vi.fn(),
      clearError: vi.fn(),
    });

    render(<UserList />);

    expect(screen.getByText('Loading users...')).toBeInTheDocument();
  });

  it('renders error state', () => {
    const mockClearError = vi.fn();
    mockUseUserStore.mockReturnValue({
      users: [],
      loading: false,
      error: 'Failed to fetch users',
      fetchUsers: vi.fn(),
      clearError: mockClearError,
    });

    render(<UserList />);

    expect(screen.getByText('Failed to fetch users')).toBeInTheDocument();
    expect(screen.getByText('Dismiss')).toBeInTheDocument();
  });

  it('renders users list', () => {
    const mockUsers = [
      { id: 1, name: 'John Doe', email: '<EMAIL>', role: 'admin' },
      { id: 2, name: 'Jane Smith', email: '<EMAIL>', role: 'user' },
    ];

    mockUseUserStore.mockReturnValue({
      users: mockUsers,
      loading: false,
      error: null,
      fetchUsers: vi.fn(),
      clearError: vi.fn(),
    });

    render(<UserList />);

    expect(screen.getByText('Users (2)')).toBeInTheDocument();
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('admin')).toBeInTheDocument();
    expect(screen.getByText('user')).toBeInTheDocument();
  });

  it('renders empty state', () => {
    mockUseUserStore.mockReturnValue({
      users: [],
      loading: false,
      error: null,
      fetchUsers: vi.fn(),
      clearError: vi.fn(),
    });

    render(<UserList />);

    expect(screen.getByText('Users (0)')).toBeInTheDocument();
    expect(screen.getByText('No users found')).toBeInTheDocument();
  });
});
